import { Pinecone, QueryOptions, QueryResponse } from '@pinecone-database/pinecone'

const namespace = 'smf'
const imagesIndexName = process.env.PINECONE_INDEX_NAME
const textIndexName = process.env.PINECONE_TEXT_SEARCH_INDEX_NAME

const pc = new Pinecone({
  apiKey: process.env.PINECONE_API_KEY
})

const MAX_RETRIES = 5
const RETRY_DELAY = 1000

export async function getSimilarProductsForImage(
  sourceVectors: any,
  gender?: string,
  categories?: string[]
): Promise<QueryResponse> {
  return await getProducts(sourceVectors, imagesIndexName, null, gender, categories)
}

export async function getSimilarProductsForText(
  sourceVectors: any,
  brand?: string,
  gender?: string,
  categories?: string[]
): Promise<QueryResponse> {
  return await getProducts(sourceVectors, textIndexName, brand, gender, categories)
}

export async function getProducts(
  sourceVectors: any,
  indexName: string,
  brand?: string,
  gender?: string,
  categories?: string[]
): Promise<QueryResponse> {
  let retries = 0

  while (retries < MAX_RETRIES) {
    try {
      const index = pc.index(indexName)

      const queryParams: any = {
        topK: 100,
        vector: sourceVectors,
        includeValues: false,
        includeMetadata: true
      }

      // Build filter dictionary if any filters are provided
      const filterConditions: any = {}

      if (brand) {
        filterConditions.brand = { $eq: brand }
      }

      if (gender) {
        // Include both the specified gender and unisex products
        filterConditions.gender = { $in: [gender, 'unisex'] }
      }

      if (categories && categories.length > 0) {
        if (categories.length === 1) {
          filterConditions.category = { $eq: categories[0] }
        } else {
          filterConditions.category = { $in: categories }
        }
      }

      // Add filter to query params if any conditions were set
      if (Object.keys(filterConditions).length > 0) {
        queryParams.filter = filterConditions
      }

      // console.debug(`Pinecone query params: ${JSON.stringify(queryParams)}`)
      console.debug(`Pinecone query filters: ${JSON.stringify(queryParams.filter)}`)
      const result = await index.query(queryParams)

      // console.debug(`Pinecone response: ${JSON.stringify(result)}`)

      return result
    } catch (error) {
      retries++
      console.error(`Error in getSimilarProducts (attempt ${retries}/${MAX_RETRIES}): ${error.message}`)

      if (retries >= MAX_RETRIES) {
        throw new Error(`Failed to get similar products after ${MAX_RETRIES} attempts: ${error.message}`)
      }

      // Wait for RETRY_DELAY milliseconds before the next attempt
      await new Promise(resolve => setTimeout(resolve, RETRY_DELAY))
    }
  }

  // This line should never be reached due to the throw in the catch block,
  // but TypeScript needs it for type safety
  throw new Error('Unexpected error in getSimilarProducts')
}
