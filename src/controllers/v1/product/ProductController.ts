import { Request, Response } from 'express'
import { ProductService } from '../../../services/v1/product/ProductService'
import { Product } from '@prisma/client'

class ProductController {
  private productService: ProductService

  constructor() {
    this.productService = new ProductService()
  }

  get = async(req: Request, res: Response) => {
    const { id } = req.params

    try {
      const product: Product = await this.productService.getProductById(id)

      if (!product) {
        return res.status(404).json({
          error: 'Product not found'
        })
      }

      const response = {
        name: product.name,
        description: product.description,
        url: product.productAffiliateUrl,
        price: product.price,
        oldPrice: product.oldPrice,
        brand: product.brand,
        imageUrl: product.imageUrl
      }

      return res.json(response)
    } catch (e) {
      return res.status(500).json({
        error: 'Internal server error'
      })
    }
  }

  getMatch = async(req: Request, res: Response) => {
    const { match_id } = req.params

    try {
      const match = await this.productService.getMatch(match_id)

      return res.json(match)
    } catch (error) {
      console.log(error)
      return res.status(400).json(error)
    }
  }

  markAsExact = async(req: Request, res: Response) => {
    const userId = req.user_id
    const { selectedMatchId } = req.body

    try {
      const udpateProduct = await this.productService.markAsExact({
        productMatchId: selectedMatchId,
        userId
      })

      return res.json(udpateProduct)
    } catch (error) {
      console.log(error)
      return res.status(400).json(error)
    }
  }

  pin = async(req: Request, res: Response) => {
    const userId = req.user_id
    const { selectedMatchId, productId } = req.body

    try {
      const udpateProduct = await this.productService.pin({
        productId,
        productMatchId: selectedMatchId,
        userId
      })

      return res.json(udpateProduct)
    } catch (error) {
      console.log(error)
      return res.status(400).json(error)
    }
  }

  createProductCropped = async(req: Request, res: Response) => {
    try {
      const userId = req.user_id
      const { selectedProductId, mediaId, boundingBox } = req.body

      const result = await this.productService.createProductCropped({
        userId,
        selectedProductId,
        mediaId,
        boundingBox
      })

      return res.json(result)
    } catch (error) {
      console.log(error)
      return res.status(500).json(error)
    }
  }

  deleteProductCropped = async(req: Request, res: Response) => {
    try {
      const userId = req.user_id
      const { selectedMatchId } = req.body

      const result = await this.productService.deleteProductCropped({
        userId,
        selectedMatchId
      })

      return res.json(result)
    } catch (error) {
      console.log(error)
      return res.status(500).json(error)
    }
  }

  searchProductCropped = async(req: Request, res: Response) => {
    const userId = req.user_id
    const { boundingBox, mediaId, searchTerm, brand } = req.body

    const result = await this.productService.searchProductCropped({
      userId,
      boundingBox,
      mediaId,
      searchTerm,
      brand
    })

    return res.json(result)
  }

  similarProducts = async(req: Request, res: Response) => {
    try {
      const { match_id } = req.params

      const result = await this.productService.getSimilarProducts({
        match_id
      })

      return res.json(result)
    } catch (error) {
      console.log(error)
      return res.status(500).json(error)
    }
  }

  createProductWithMatch = async(req: Request, res: Response) => {
    try {
      const searchSimilarProductService = new ProductService()

      const result = await searchSimilarProductService.createProductWithMatch(
        {
          product: req.body.product,
          matchId: req.body.match_id,
          userId: req.body.user_id
        }
      )

      return res.json(result)
    } catch (error) {
      console.log(error)
      return res.status(500).json(error)
    }
  }
}

export { ProductController }
