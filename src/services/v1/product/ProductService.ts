import prismaClient from '../../../prisma'
import { Product, Role } from '@prisma/client'
import { UserService } from '../user/UserService'
import { CloudTasksClient, protos } from '@google-cloud/tasks'
import { CreateProductCroppedInterface, CreateProductWithMatchInterface, DeleteProductCroppedInterface, SearchProductCroppedInterface, SimilarProductProps } from '../../../types/v1/product'
import { getProducts, getSimilarProductsForText } from '../../../api/pinecone/vectors'
import { getEmbeddingsForImage, getEmbeddingsForText } from '../../../api/azureAI/embeddings'
import { getPhoto } from '../../../utils/downloadImage'
import { cropImage } from '../../../helper/cropImage'
import { randomUUID } from 'node:crypto'

type CreateTaskRequest = protos.google.cloud.tasks.v2.ICreateTaskRequest;

interface ProductMatchMark {
    productId: string
    productMatchId: string
    userId: string
}

class ProductService {
  private tasksClient: CloudTasksClient
  private projectId: string
  private queueName: string
  private location: string
  private pythonServiceUrl: string

  constructor() {
    this.tasksClient = new CloudTasksClient()
    this.projectId = process.env.GOOGLE_PROJECT_ID || ''
    this.queueName = process.env.TASK_QUEUE_NAME || 'smf-ai-tasks-queue-dev'
    this.location = process.env.CLOUD_TASKS_LOCATION || 'us-east1'
    this.pythonServiceUrl = process.env.AI_SERVICE_URL || 'https://smf-ai-dev-************.us-east1.run.app'
  }

  async getProductById(id: string): Promise<Product> | null {
    return prismaClient.product.findFirst({
      where: {
        OR: [
          { id },
          { externalId: id }
        ]
      }
    })
  }

  async getMatch(id: string) {
    return prismaClient.postProductMatch.findFirst({
      where: {
        id
      },
      select: {
        id: true,
        isExact: true,
        pinnedProducts: true,
        mediaId: true,
        postId: true,
        post: true,
        productId: true,
        product: true,
        score: true,
        userId: true,
        detectedObject: true,
        createdAt: true,
        updatedAt: true
      }
    })
  }

  async pin({ productId, productMatchId, userId }: ProductMatchMark) {
    const userService = new UserService()
    const user = await userService.getUserById(userId)

    if (!user) {
      throw new Error('User not found')
    }

    if (user.role !== Role.ADMIN && user.role !== Role.INFLUENCER) {
      throw new Error('User is not an admin or influencer')
    }

    const postProductMatch = await prismaClient.postProductMatch.findFirst({
      where: {
        id: productMatchId
      }
    })

    const product = await prismaClient.product.findFirst({
      where: {
        id: productId
      }
    })

    if (!postProductMatch && !product) {
      throw new Error('Post product match not found')
    }

    const updatedPostProductMatch = await prismaClient.postProductMatch.update({
      where: {
        id: postProductMatch.id
      },
      data: {
        pinnedProducts: {
          create: {
            product: {
              connect: {
                id: product.id
              }
            }
          }
        }
      }
    })

    return updatedPostProductMatch
  }

  async markAsExact({ productMatchId, userId }: Partial<ProductMatchMark>) {
    const userService = new UserService()
    const user = await userService.getUserById(userId)

    if (!user) {
      throw new Error('User not found')
    }

    if (user.role !== Role.ADMIN && user.role !== Role.INFLUENCER) {
      throw new Error('User is not an admin or influencer')
    }

    const postProductMatch = await prismaClient.postProductMatch.findFirst({
      where: {
        id: productMatchId
      }
    })

    if (!postProductMatch) {
      throw new Error('Post product match not found')
    }

    const updatedPostProductMatch = await prismaClient.postProductMatch.update({
      where: {
        id: postProductMatch.id
      },
      data: {
        isExact: !postProductMatch.isExact
      }
    })

    return updatedPostProductMatch
  }

  async extractProductsForPost(postId: string) {
    const taskPayload = {
      taskType: 'extract_products',
      data: {
        post_id: postId,
        timestamp: new Date().toISOString()
      }
    }

    // Get the fully-qualified queue path
    const queuePath = this.tasksClient.queuePath(
      this.projectId,
      this.location,
      this.queueName
    )

    const request: CreateTaskRequest = {
      parent: queuePath,
      task: {
        httpRequest: {
          httpMethod: 'POST',
          url: `${this.pythonServiceUrl}/process-task`,
          headers: {
            'Content-Type': 'application/json'
          },
          body: Buffer.from(JSON.stringify(taskPayload)).toString('base64'),
          oidcToken: {
            serviceAccountEmail: `${process.env.AI_SERVICE_ACCOUNT_EMAIL || 'smf-ai-sa-dev'}@${this.projectId}.iam.gserviceaccount.com`
          }
        }
      }
    }

    try {
      // Send the task to the queue
      const [response] = await this.tasksClient.createTask(request)
      return {
        success: true,
        taskName: response.name,
        message: 'Product extraction task created successfully'
      }
    } catch (error) {
      console.error('Error creating product extraction task:', error)
      throw new Error('Failed to create product extraction task')
    }
  }

  async getSimilarProducts({ match_id }: SimilarProductProps) {
    const results: any[] = []
    const postProductMatch = await prismaClient.postProductMatch.findUnique({
      where: {
        id: match_id
      },
      select: {
        embeddings: true,
        detectedObject: true
      }
    })

    console.log(`Similar products for match ${match_id}. Embeddings: ${postProductMatch.embeddings}`)

    // Extract categories and gender from detectedObject if available
    let categories: string[] = []
    let gender: string = null

    if (postProductMatch.detectedObject) {
      try {
        // Parse detectedObject if it's a string
        const detectedObj = typeof postProductMatch.detectedObject === 'string'
          ? JSON.parse(postProductMatch.detectedObject)
          : postProductMatch.detectedObject

        // Extract categories if available
        if (detectedObj.categories) {
          // Categories might be stored as a pipe-separated string
          if (typeof detectedObj.categories === 'string') {
            categories = detectedObj.categories.split('|').filter(Boolean)
          } else if (Array.isArray(detectedObj.categories)) {
            categories = detectedObj.categories.filter(Boolean)
          }
        }

        // Extract gender if available
        if (detectedObj.gender) {
          gender = detectedObj.gender
        }

        console.log(`Extracted filters - Categories: ${categories.join(', ')}, Gender: ${gender}`)
      } catch (error) {
        console.error(`Error parsing detectedObject: ${error.message}`)
      }
    }

    const similarProducts = await getProducts(
      postProductMatch.embeddings,
      process.env.PINECONE_INDEX_NAME,
      null, // brand filter
      gender,
      categories.length > 0 ? categories : null
    )

    if (!similarProducts || !similarProducts.matches) {
      throw new Error('No similar products found')
    }

    await Promise.all(similarProducts.matches.map(async(match: any) => {
      const product = await prismaClient.product.findUnique({
        where: {
          externalId: match.id
        }
      })

      if (product) {
        results.push(product)
      }
    }))

    return results
  }

  async searchProductCropped({ userId, boundingBox, mediaId, searchTerm, brand }: SearchProductCroppedInterface) {
    const userService = new UserService()
    const user = await userService.getUserById(userId)

    if (!user) {
      throw new Error('User not found')
    }

    if (!(user.role === Role.ADMIN || user.role === Role.INFLUENCER)) {
      throw new Error('User is not an admin or influencer')
    }

    const vectorsText = await getEmbeddingsForText(searchTerm)
    const resultPineconeText = await getSimilarProductsForText(vectorsText, brand, null, null)

    return resultPineconeText.matches
  }

  async createProductCropped({ userId, selectedProductId, mediaId, boundingBox }: CreateProductCroppedInterface) {
    const userService = new UserService()
    const user = await userService.getUserById(userId)

    if (!user) {
      throw new Error('User not found')
    }

    if (!(user.role === Role.ADMIN || user.role === Role.INFLUENCER)) {
      throw new Error('User is not an admin or influencer')
    }

    const productMedia = await prismaClient.media.findUnique({
      where: {
        id: mediaId
      }
    })

    if (!productMedia) {
      throw new Error('Media not found')
    }

    const productMatch = await prismaClient.product.findFirst({
      where: {
        externalId: selectedProductId
      }
    })

    if (!productMatch) {
      throw new Error('Product not found')
    }

    const productUrl = productMedia.mediaUrl

    const productImage = await getPhoto(productUrl, true)

    const croppedImage = await cropImage(productImage, boundingBox)

    const embeddings = await getEmbeddingsForImage(croppedImage.externalUrl)

    const postProductMatch = await prismaClient.postProductMatch.create({
      data: {
        score: 0,
        mediaId,
        userId,
        detectedObject: JSON.stringify(boundingBox),
        embeddings,
        postId: productMedia.postId,
        productId: productMatch.id
      }
    })

    return postProductMatch
  }

  async deleteProductCropped({ userId, selectedMatchId }: DeleteProductCroppedInterface) {
    const userService = new UserService()
    const user = await userService.getUserById(userId)

    if (!user) {
      throw new Error('User not found')
    }

    if (!(user.role === Role.ADMIN || user.role === Role.INFLUENCER)) {
      throw new Error('User is not an admin or influencer')
    }

    const postProductMatch = await prismaClient.postProductMatch.findFirst({
      where: {
        id: selectedMatchId
      }
    })

    if (!postProductMatch) {
      throw new Error('Post product match not found')
    }

    const deletedProduct = await prismaClient.postProductMatch.delete({
      where: {
        id: postProductMatch.id
      }
    })

    return deletedProduct
  }

  async createProductWithMatch({ product, matchId, userId }: CreateProductWithMatchInterface) {
    const authUser = await prismaClient.user.findFirst({
      where: {
        id: userId
      }
    })

    if (!authUser) {
      throw new Error('User not found')
    }
    const postProductMatch = await prismaClient.postProductMatch.findFirst({
      where: {
        id: matchId
      }
    })

    if (!postProductMatch) {
      throw new Error('Post product match not found')
    }

    if (authUser.role !== Role.ADMIN && authUser.id !== postProductMatch.userId) {
      throw new Error('User is not authorized')
    }

    const createdProduct = await prismaClient.product.create({
      data: {
        ...product,
        externalId: randomUUID(),
        approved: true,
        alternative: false
      }
    })

    await prismaClient.postProductMatch.update({
      where: {
        id: postProductMatch.id
      },
      data: {
        productId: createdProduct.id
      }
    })

    return createdProduct
  }
}

export { ProductService }
